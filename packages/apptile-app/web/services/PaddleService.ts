import { initializePaddle, Paddle } from '@paddle/paddle-js';

export interface PaddlePlan {
  id: string;
  name: string;
  description: string;
  billing_cycle: {
    interval: 'month' | 'year';
    frequency: number;
  };
  unit_price: {
    amount: string;
    currency_code: string;
  };
  custom_data?: {
    credits?: string;
    popular?: boolean;
    features?: string[];
  };
}

export interface PaddleCheckoutOptions {
  items: Array<{
    priceId: string;
    quantity?: number;
  }>;
  customer?: {
    email?: string;
    id?: string;
  };
  customData?: {
    organizationId?: string;
    userId?: string;
    credits?: string;
  };
  successUrl?: string;
  settings?: {
    displayMode?: 'inline' | 'overlay';
    theme?: 'light' | 'dark';
    locale?: string;
  };
}

class PaddleService {
  private paddle: Paddle | null = null;
  private clientToken: string;
  private isInitialized: boolean = false;

  constructor(clientToken: string) {
    this.clientToken = clientToken;
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      this.paddle = await initializePaddle({
        token: this.clientToken,
        environment: process.env.NODE_ENV === 'production' ? 'production' : 'sandbox',
        eventCallback: this.handlePaddleEvent.bind(this),
      });
      this.isInitialized = true;
      console.log('Paddle initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Paddle:', error);
      throw error;
    }
  }

  private handlePaddleEvent(event: any): void {
    console.log('Paddle event:', event);
    
    switch (event.name) {
      case 'checkout.completed':
        this.handleCheckoutCompleted(event.data);
        break;
      case 'checkout.closed':
        this.handleCheckoutClosed(event.data);
        break;
      case 'checkout.error':
        this.handleCheckoutError(event.data);
        break;
      default:
        console.log('Unhandled Paddle event:', event.name);
    }
  }

  private handleCheckoutCompleted(data: any): void {
    console.log('Checkout completed:', data);
    // Handle successful checkout
    // This will be called when payment is successful
    // Simulate adding credits to organization
    this.simulateAddCredits(data);
  }

  private simulateAddCredits(checkoutData: any): void {
    // Simulate API call to add credits
    const credits = checkoutData.customData?.credits || '100';
    const organizationId = checkoutData.customData?.organizationId;

    console.log(`Simulating adding ${credits} credits to organization ${organizationId}`);

    // Show success message
    alert(`Success! ${credits} credits have been added to your organization.`);

    // In a real implementation, you would dispatch a Redux action here
    // to update the organization's credit balance
  }

  private handleCheckoutClosed(data: any): void {
    console.log('Checkout closed:', data);
    // Handle checkout modal being closed
  }

  private handleCheckoutError(data: any): void {
    console.error('Checkout error:', data);
    // Handle checkout errors
  }

  async getPlans(): Promise<PaddlePlan[]> {
    if (!this.paddle) {
      throw new Error('Paddle not initialized');
    }

    try {
      // In a real implementation, you would fetch plans from Paddle API
      // For now, we'll return mock data that matches Lovable's structure
      return this.getMockPlans();
    } catch (error) {
      console.error('Failed to fetch plans:', error);
      throw error;
    }
  }

  private getMockPlans(): PaddlePlan[] {
    return [
      {
        id: 'free-plan',
        name: 'Free',
        description: 'For getting started',
        billing_cycle: {
          interval: 'month',
          frequency: 1,
        },
        unit_price: {
          amount: '0',
          currency_code: 'USD',
        },
        custom_data: {
          credits: '5',
          popular: false,
          features: ['Public projects'],
        },
      },
      {
        id: 'pro-plan-100',
        name: 'Pro',
        description: 'For more projects and usage',
        billing_cycle: {
          interval: 'month',
          frequency: 1,
        },
        unit_price: {
          amount: '25',
          currency_code: 'USD',
        },
        custom_data: {
          credits: '100',
          popular: true,
          features: ['Everything in Free', 'Private projects', 'Priority support'],
        },
      },
      {
        id: 'pro-plan-200',
        name: 'Pro',
        description: 'For more projects and usage',
        billing_cycle: {
          interval: 'month',
          frequency: 1,
        },
        unit_price: {
          amount: '45',
          currency_code: 'USD',
        },
        custom_data: {
          credits: '200',
          popular: false,
          features: ['Everything in Free', 'Private projects', 'Priority support'],
        },
      },
      {
        id: 'teams-plan',
        name: 'Teams',
        description: 'For collaborating with others',
        billing_cycle: {
          interval: 'month',
          frequency: 1,
        },
        unit_price: {
          amount: '30',
          currency_code: 'USD',
        },
        custom_data: {
          credits: '100',
          popular: false,
          features: ['Everything in Pro', 'Centralized billing', 'Centralized access management', 'Includes 20 seats'],
        },
      },
    ];
  }

  async openCheckout(options: PaddleCheckoutOptions): Promise<void> {
    if (!this.paddle) {
      throw new Error('Paddle not initialized');
    }

    try {
      await this.paddle.Checkout.open({
        items: options.items,
        customer: options.customer,
        customData: options.customData,
        successUrl: options.successUrl || window.location.origin + '/payment-success',
        settings: {
          displayMode: 'overlay',
          theme: 'light',
          locale: 'en',
          ...options.settings,
        },
      });
    } catch (error) {
      console.error('Failed to open checkout:', error);
      throw error;
    }
  }

  async getPrices(productIds: string[]): Promise<any[]> {
    if (!this.paddle) {
      throw new Error('Paddle not initialized');
    }

    try {
      // In a real implementation, you would fetch prices from Paddle API
      // For now, return mock data
      return [];
    } catch (error) {
      console.error('Failed to fetch prices:', error);
      throw error;
    }
  }
}

// Singleton instance
let paddleService: PaddleService | null = null;

export const getPaddleService = (clientToken?: string): PaddleService => {
  if (!paddleService) {
    if (!clientToken) {
      throw new Error('Client token required for first initialization');
    }
    paddleService = new PaddleService(clientToken);
  }
  return paddleService;
};

export default PaddleService;

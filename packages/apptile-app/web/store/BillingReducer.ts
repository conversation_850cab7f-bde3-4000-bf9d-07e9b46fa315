import {union} from 'lodash';
import {combineReducers} from 'redux';
import {createReducer} from 'apptile-core';
import {
  CONFIRM_CHARGE,
  CONFIRM_CHARGE_FAILED,
  CONFIRM_CHARGE_SUCCESS,
  DispatchAction,
  <PERSON>ET<PERSON>_ADD_ON_PAYMENT_URL,
  <PERSON>ET<PERSON>_MY_ADD_ONS,
  FETCH_MY_ADD_ONS_FAILED,
  FETCH_MY_ADD_ONS_SUCCESS,
  FETCH_MY_SUBSCRIPTION,
  FETCH_MY_SUBSCRIPTION_FAILED,
  FETCH_MY_SUBSCRIPTION_SUCCESS,
  FETCH_PAYMENT_URL,
  FETCH_PAYMENT_URL_FAILED,
  FETCH_PAYMENT_URL_SUCCESS,
  <PERSON>ETCH_PLANS_LIST,
  FETCH_PLANS_LIST_SUCCESS,
  FETCH_PLAN_SUCCESS,
  PADDLE_INIT,
  PADDLE_INIT_SUCCESS,
  PADD<PERSON>_INIT_FAILED,
  PAD<PERSON><PERSON>_FET<PERSON>_PLANS,
  PADD<PERSON>_FETCH_PLANS_SUCCESS,
  PADD<PERSON>_FETCH_PLANS_FAILED,
  PADDLE_OPEN_CHECKOUT,
  PADDLE_CHECKOUT_SUCCESS,
  PADDLE_CHECKOUT_FAILED,
  PADDLE_ADD_CREDITS,
  PADDLE_ADD_CREDITS_SUCCESS,
  PADDLE_ADD_CREDITS_FAILED,
} from '../actions/editorActions';
import {
  FetchNormalizedResponse,
  FetchPaymentURLResponse,
  ICurrentSubscription,
  ISubscriptionPlan,
  PaddleState,
  PaddlePlan,
  OrganizationCredits,
} from '../api/ApiTypes';

export interface SubscriptionState {
  confirmationUrl: string;
  currentSubscription: ICurrentSubscription | null;
  currentCharge: ICurrentSubscription | null;
  currentChargeLoading: boolean;
  currentChargeFetched: boolean;
  paymentUrlFetched: boolean;
  paymentUrlFetching: boolean;
  fetchPlansLoading: boolean;
}

const initialState: SubscriptionState = {
  confirmationUrl: '',
  currentSubscription: null,
  currentCharge: null,
  currentChargeLoading: false,
  currentChargeFetched: false,
  paymentUrlFetched: false,
  paymentUrlFetching: false,
  fetchPlansLoading: false,
};

const initialPaddleState: PaddleState = {
  isInitialized: false,
  plans: [],
  loading: false,
  error: null,
  checkoutInProgress: false,
};

export interface BillingReduxState {
  subscription: SubscriptionState;
  planIds: Array<string>;
  plansById: Record<string, ISubscriptionPlan>;
  addOnIds: Array<String>;
  paddle: PaddleState;
  organizationCredits: OrganizationCredits | null;
}

export const subscription = createReducer<SubscriptionState>(
  {
    [FETCH_PAYMENT_URL]: state => {
      return {
        ...state,
        ...{
          paymentUrlFetched: false,
          confirmationUrl: '',
          paymentUrlFetching: true,
        },
      };
    },
    [FETCH_ADD_ON_PAYMENT_URL]: state => {
      return {
        ...state,
        ...{
          paymentUrlFetched: false,
          confirmationUrl: '',
          paymentUrlFetching: true,
        },
      };
    },
    [FETCH_PAYMENT_URL_SUCCESS]: (state, action: DispatchAction<FetchPaymentURLResponse>) => {
      return {
        ...state,
        confirmationUrl: action.payload,
        paymentUrlFetched: true,
        paymentUrlFetching: false,
      };
    },
    [FETCH_PAYMENT_URL_FAILED]: state => {
      return {
        ...state,
        ...{
          paymentUrlFetched: true,
          confirmationUrl: '',
          paymentUrlFetching: false,
        },
      };
    },

    [FETCH_MY_SUBSCRIPTION]: state => {
      return {
        ...state,
        ...{
          currentSubscription: null,
        },
      };
    },
    [FETCH_MY_SUBSCRIPTION_SUCCESS]: (state, action: DispatchAction<any>) => {
      return {
        ...state,
        currentSubscription: action.payload,
        fetchPlansLoading: false,
      };
    },
    [FETCH_MY_SUBSCRIPTION_FAILED]: state => {
      return {
        ...state,
        currentSubscription: null,
        fetchPlansLoading: false,
      };
    },

    [CONFIRM_CHARGE]: state => {
      return {
        ...state,
        ...{
          currentCharge: null,
          currentChargeLoading: true,
          currentChargeFetched: false,
        },
      };
    },
    [CONFIRM_CHARGE_SUCCESS]: (state, action: DispatchAction<any>) => {
      return {
        ...state,
        currentCharge: action.payload,
        currentChargeLoading: false,
        currentChargeFetched: true,
      };
    },
    [CONFIRM_CHARGE_FAILED]: state => {
      return {
        ...state,
        currentCharge: null,
        currentChargeLoading: false,
        currentChargeFetched: true,
      };
    },
    [FETCH_PLANS_LIST]: state => {
      return {
        ...state,
        fetchPlansLoading: true,
      };
    },
  },
  //DefaultState
  initialState,
);

const planIds = createReducer<Array<number>>(
  {
    [FETCH_PLANS_LIST]: state => {
      return [];
    },
    [FETCH_PLANS_LIST_SUCCESS]: (state, action: DispatchAction<FetchNormalizedResponse<ISubscriptionPlan>>) => {
      return union(state, action.payload?.result);
    },
    [FETCH_PLAN_SUCCESS]: (state, action: DispatchAction<FetchNormalizedResponse<ISubscriptionPlan>>) => {
      return union(state, [action.payload?.result]);
    },
    // [FETCH_PLANS_LIST_FAILED]: (state, action: DispatchAction<FetchNormalizedResponse<ISubscriptionPlan>>) => {
    //   return union(state, [action.payload?.result]);
    // },
  },
  //DefaultState
  [],
);

const plansById = createReducer<Record<string, ISubscriptionPlan>>(
  {
    [FETCH_PLANS_LIST]: state => {
      return {};
    },
    [FETCH_PLANS_LIST_SUCCESS]: (state, action: DispatchAction<FetchNormalizedResponse<ISubscriptionPlan>>) => {
      return {...state, ...action.payload?.entities?.items};
    },
    [FETCH_PLAN_SUCCESS]: (state, action: DispatchAction<FetchNormalizedResponse<ISubscriptionPlan>>) => {
      return {...state, ...action.payload?.entities?.items};
    },
    // [FETCH_PLANS_LIST_FAILED]: (state, action: DispatchAction<FetchNormalizedResponse<ISubscriptionPlan>>) => {
    //   return {...action.payload?.entities?.items, ...state};
    // },
  },
  //DefaultState
  {},
);

const addOnIds = createReducer<Array<number>>(
  {
    [FETCH_MY_ADD_ONS]: state => {
      return [];
    },
    [FETCH_MY_ADD_ONS_SUCCESS]: (state, action: DispatchAction<FetchNormalizedResponse<[string]>>) => {
      return union(state, action.payload);
    },
    [FETCH_MY_ADD_ONS_FAILED]: (state, action: DispatchAction<FetchNormalizedResponse<[string]>>) => {
      return union(state, [action.payload?.result]);
    },
    // [FETCH_PLANS_LIST_FAILED]: (state, action: DispatchAction<FetchNormalizedResponse<ISubscriptionPlan>>) => {
    //   return union(state, [action.payload?.result]);
    // },
  },
  //DefaultState
  [],
);

const paddle = createReducer<PaddleState>(
  {
    [PADDLE_INIT]: state => ({
      ...state,
      loading: true,
      error: null,
    }),
    [PADDLE_INIT_SUCCESS]: state => ({
      ...state,
      isInitialized: true,
      loading: false,
      error: null,
    }),
    [PADDLE_INIT_FAILED]: (state, action: DispatchAction<{ error: string }>) => ({
      ...state,
      isInitialized: false,
      loading: false,
      error: action.payload.error,
    }),
    [PADDLE_FETCH_PLANS]: state => ({
      ...state,
      loading: true,
      error: null,
    }),
    [PADDLE_FETCH_PLANS_SUCCESS]: (state, action: DispatchAction<{ plans: PaddlePlan[] }>) => ({
      ...state,
      plans: action.payload.plans,
      loading: false,
      error: null,
    }),
    [PADDLE_FETCH_PLANS_FAILED]: (state, action: DispatchAction<{ error: string }>) => ({
      ...state,
      loading: false,
      error: action.payload.error,
    }),
    [PADDLE_OPEN_CHECKOUT]: state => ({
      ...state,
      checkoutInProgress: true,
      error: null,
    }),
    [PADDLE_CHECKOUT_SUCCESS]: state => ({
      ...state,
      checkoutInProgress: false,
      error: null,
    }),
    [PADDLE_CHECKOUT_FAILED]: (state, action: DispatchAction<{ error: string }>) => ({
      ...state,
      checkoutInProgress: false,
      error: action.payload.error,
    }),
  },
  initialPaddleState,
);

const organizationCredits = createReducer<OrganizationCredits | null>(
  {
    [PADDLE_ADD_CREDITS_SUCCESS]: (state, action: DispatchAction<{ credits: OrganizationCredits }>) => {
      return action.payload.credits;
    },
    [PADDLE_ADD_CREDITS_FAILED]: state => {
      return state;
    },
  },
  null,
);

export const BillingReducer = combineReducers({
  subscription: subscription,
  planIds: planIds,
  plansById: plansById,
  addOnIds: addOnIds,
  paddle: paddle,
  organizationCredits: organizationCredits,
});

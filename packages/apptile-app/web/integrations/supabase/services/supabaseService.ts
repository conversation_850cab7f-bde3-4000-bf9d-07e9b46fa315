import {Supa<PERSON><PERSON><PERSON>} from './Supabase<PERSON>pi';

// Define the Supabase configuration interface
export interface SupabaseConfig {
  SUPABASE_ANON_KEY: string;
  SUPABASE_PROJECT_REF: string;
  SUPABASE_ACCESS_TOKEN: string;
  SUPABASE_REFRESH_TOKEN: string;
  ACCESS_TOKEN_EXPIRES_IN: number;
  APPTILE_SUPABASE_CLIENT_ID: string;
  APPTILE_SUPABASE_CLIENT_SECRET: string;
}

/**
 * Installs the Apptile integration in the user's Supabase account
 * This function is a wrapper around the SupabaseApi.connectToProject method
 */
export const installSupabaseIntegration = async (config: SupabaseConfig): Promise<void> => {
  try {
    // Get the current app ID from the Redux store
    const appId = SupabaseApi.getCurrentAppId();

    if (!appId) {
      throw new Error('App ID not found');
    }

    // Get the project details
    const projects = await Supa<PERSON><PERSON><PERSON>.listProjects(config.SUPABASE_ACCESS_TOKEN);

    // Find the project with the matching project reference
    const project = projects.find(p => p.id === config.SUPABASE_PROJECT_REF);

    if (!project) {
      throw new Error(`Project with reference ${config.SUPABASE_PROJECT_REF} not found`);
    }

    // Connect to the project
    await SupabaseApi.connectToProject(appId, project, {
      supabaseAccessToken: config.SUPABASE_ACCESS_TOKEN,
      supabaseRefreshToken: config.SUPABASE_REFRESH_TOKEN,
      accessTokenExpiresIn: config.ACCESS_TOKEN_EXPIRES_IN,
      apptileSupabaseClientId: config.APPTILE_SUPABASE_CLIENT_ID,
      apptileSupabaseClientSecret: config.APPTILE_SUPABASE_CLIENT_SECRET,
    });

    console.log('Supabase integration successfully installed!');
  } catch (error) {
    console.error('Error installing Supabase integration:', error);
    throw new Error('Failed to install Supabase integration');
  }
};

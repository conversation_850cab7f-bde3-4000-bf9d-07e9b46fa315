/**
 * Supabase integration configuration
 * These values should be set in the environment or configuration files
 */
export const SupabaseConfig = {
  // Client ID for the Supabase OAuth application
  CLIENT_ID: process.env.SUPABASE_CLIENT_ID || '0698540f-d9cf-45ff-8f65-2973131484a0',

  // Client secret for the Supabase OAuth application
  CLIENT_SECRET: process.env.SUPABASE_CLIENT_SECRET || 'sba_475c153298ebb94c140ce19cf533db3cdced8fbf',

  // OAuth redirect URI
  REDIRECT_URI: '/supabase-callback',

  // OAuth scopes
  SCOPES: ['read', 'write'],

  // API endpoints
  API_BASE_URL: 'https://api.supabase.com',
  OAUTH_AUTHORIZE_URL: 'https://api.supabase.com/v1/oauth/authorize',
  OAUTH_TOKEN_URL: 'https://api.supabase.com/v1/oauth/token',

  // Access token expiration time in seconds (default: 24 hours)
  ACCESS_TOKEN_EXPIRES_IN: 86400,
};

export default SupabaseConfig;

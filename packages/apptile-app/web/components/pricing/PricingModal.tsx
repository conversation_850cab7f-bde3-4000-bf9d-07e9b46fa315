import React, { useEffect, useState } from 'react';
import { View, StyleSheet, Pressable, ScrollView, Modal } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { MaterialCommunityIcons } from 'apptile-core';
import TextElement from '../../components-v2/base/TextElement';
import Button from '../../components-v2/base/Button';
// import PlanCard from './PlanCard';
import { PaddlePlan, getPaddleService } from '../../services/PaddleService';
import { EditorRootState } from '../../store/EditorRootState';
import theme from '../../styles-v2/theme';

interface PricingModalProps {
  visible: boolean;
  onClose: () => void;
}

const PricingModal: React.FC<PricingModalProps> = ({ visible, onClose }) => {
  const dispatch = useDispatch();
  const [plans, setPlans] = useState<PaddlePlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPlan, setSelectedPlan] = useState<PaddlePlan | null>(null);
  const [billingInterval, setBillingInterval] = useState<'month' | 'year'>('month');

  const { user } = useSelector((state: EditorRootState) => state.user);
  const { orgId } = useSelector((state: EditorRootState) => state.apptile);

  useEffect(() => {
    if (visible) {
      loadPlans();
    }
  }, [visible]);

  const loadPlans = async () => {
    try {
      setLoading(true);
      const paddleService = getPaddleService('test_f0ff365cf3b22228fc6dde90827');
      await paddleService.initialize();
      const fetchedPlans = await paddleService.getPlans();
      setPlans(fetchedPlans);
    } catch (error) {
      console.error('Failed to load plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePlanSelect = (plan: PaddlePlan) => {
    setSelectedPlan(plan);
  };

  const handleUpgrade = async () => {
    if (!selectedPlan) return;

    try {
      const paddleService = getPaddleService();
      await paddleService.openCheckout({
        items: [
          {
            priceId: selectedPlan.id,
            quantity: 1,
          },
        ],
        customer: {
          email: user?.email,
          id: user?.id?.toString(),
        },
        customData: {
          organizationId: orgId,
          userId: user?.id?.toString(),
          credits: selectedPlan.custom_data?.credits,
        },
        successUrl: `${window.location.origin}/payment-success`,
      });
    } catch (error) {
      console.error('Failed to open checkout:', error);
    }
  };

  const renderContent = () => (
    <View style={styles.container}>
      <View style={styles.header}>
        <Pressable onPress={onClose} style={styles.closeButton}>
          <MaterialCommunityIcons name="close" size={24} color={theme.SECONDARY_COLOR} />
        </Pressable>
        <TextElement fontSize="3xl" fontWeight="600" color="SECONDARY" style={styles.title}>
          Pricing
        </TextElement>
        <TextElement fontSize="lg" color="EDITOR_LIGHT_BLACK" style={styles.subtitle}>
          Start for free. Upgrade to get the capacity that exactly matches your team's needs.
        </TextElement>
      </View>

      <View style={styles.billingToggle}>
        <Pressable
          style={[
            styles.toggleButton,
            billingInterval === 'month' && styles.toggleButtonActive,
          ]}
          onPress={() => setBillingInterval('month')}
        >
          <TextElement
            fontSize="sm"
            color={billingInterval === 'month' ? 'PRIMARY' : 'EDITOR_LIGHT_BLACK'}
            fontWeight={billingInterval === 'month' ? '600' : '400'}
          >
            Monthly
          </TextElement>
        </Pressable>
        <Pressable
          style={[
            styles.toggleButton,
            billingInterval === 'year' && styles.toggleButtonActive,
          ]}
          onPress={() => setBillingInterval('year')}
        >
          <TextElement
            fontSize="sm"
            color={billingInterval === 'year' ? 'PRIMARY' : 'EDITOR_LIGHT_BLACK'}
            fontWeight={billingInterval === 'year' ? '600' : '400'}
          >
            Yearly
          </TextElement>
        </Pressable>
      </View>

      <ScrollView style={styles.plansContainer} showsVerticalScrollIndicator={false}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <TextElement fontSize="lg" color="EDITOR_LIGHT_BLACK">
              Loading plans...
            </TextElement>
          </View>
        ) : (
          <ScrollView
            horizontal
            style={{ paddingBottom: 24 }}
            contentContainerStyle={styles.plansGrid}
          >
            {plans.map((plan) => (
              <Pressable
                key={plan.id}
                style={[
                  styles.planCard,
                  selectedPlan?.id === plan.id && styles.planCardSelected,
                ]}
                onPress={() => handlePlanSelect(plan)}
              >
                <TextElement fontSize="xl" fontWeight="600" color="SECONDARY">
                  {plan.name} - ${plan.unit_price.amount}/{billingInterval}
                </TextElement>
                <TextElement fontSize="sm" color="EDITOR_LIGHT_BLACK" style={{ marginTop: 8 }}>
                  {plan.description}
                </TextElement>
                {plan.custom_data?.credits && (
                  <TextElement fontSize="sm" color="EDITOR_LIGHT_BLACK" style={{ marginTop: 8 }}>
                    {plan.custom_data.credits} credits / month
                  </TextElement>
                )}
              </Pressable>
            ))}
          </ScrollView>
        )}
      </ScrollView>

      {selectedPlan && selectedPlan.id !== 'free-plan' && (
        <View style={styles.footer}>
          <Button
            color="CTA"
            size="LARGE"
            onPress={handleUpgrade}
            containerStyles={styles.upgradeButton}
          >
            Upgrade to {selectedPlan.name}
          </Button>
        </View>
      )}
    </View>
  );

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <Pressable
        style={styles.modalBackground}
        onPress={onClose}
      >
        <Pressable
          style={styles.modalContainer}
          onPress={(e) => e.stopPropagation()}
        >
          {renderContent()}
        </Pressable>
      </Pressable>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: 900,
    maxWidth: '90vw',
    maxHeight: '90vh',
    backgroundColor: theme.DEFAULT_COLOR,
    borderRadius: 16,
    padding: 0,
    overflow: 'hidden',
  },
  header: {
    padding: 32,
    paddingBottom: 24,
    borderBottomWidth: 1,
    borderBottomColor: theme.QUATERNARY_BACKGROUND,
    alignItems: 'center',
  },
  closeButton: {
    position: 'absolute',
    top: 16,
    right: 16,
    padding: 8,
    zIndex: 1,
  },
  title: {
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    textAlign: 'center',
    maxWidth: 500,
  },
  billingToggle: {
    flexDirection: 'row',
    backgroundColor: theme.QUATERNARY_BACKGROUND,
    borderRadius: 8,
    padding: 4,
    margin: 24,
    marginBottom: 16,
    alignSelf: 'center',
  },
  toggleButton: {
    paddingHorizontal: 24,
    paddingVertical: 8,
    borderRadius: 6,
  },
  toggleButtonActive: {
    backgroundColor: theme.DEFAULT_COLOR,
    shadowColor: theme.SECONDARY_COLOR,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  plansContainer: {
    flex: 1,
    paddingHorizontal: 24,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 200,
  },
  plansGrid: {
    flexDirection: 'row',
    gap: 16,
    paddingHorizontal: 16,
  },
  footer: {
    padding: 24,
    borderTopWidth: 1,
    borderTopColor: theme.QUATERNARY_BACKGROUND,
  },
  upgradeButton: {
    width: '100%',
  },
  planCard: {
    width: 280,
    backgroundColor: theme.DEFAULT_COLOR,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: theme.QUATERNARY_BACKGROUND,
    padding: 24,
    position: 'relative',
    minHeight: 400,
  },
  planCardSelected: {
    borderColor: theme.CTA,
    shadowColor: theme.CTA,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  planCardPopular: {
    borderColor: theme.CTA,
  },
  popularBadge: {
    position: 'absolute',
    top: -1,
    left: 24,
    right: 24,
    backgroundColor: theme.CTA,
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    alignItems: 'center',
  },
  featureItem: {
    marginVertical: 4,
  },
});

export default PricingModal;

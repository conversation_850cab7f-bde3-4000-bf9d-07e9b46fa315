import React from 'react';
import {View, StyleSheet, Pressable} from 'react-native';
import {MaterialCommunityIcons} from 'apptile-core';
import TextElement from '../../components-v2/base/TextElement';
import Button from '../../components-v2/base/Button';
import {PaddlePlan} from '../../services/PaddleService';
import theme from '../../styles-v2/theme';

interface PlanCardProps {
  plan: PaddlePlan;
  isSelected: boolean;
  onSelect: () => void;
  billingInterval: 'month' | 'year';
}

const PlanCard: React.FC<PlanCardProps> = ({plan, isSelected, onSelect, billingInterval}) => {
  const isPopular = plan.custom_data?.popular;
  const isFree = plan.id === 'free-plan';
  const credits = plan.custom_data?.credits || '0';
  const features = plan.custom_data?.features || [];

  const getPrice = () => {
    const amount = parseFloat(plan.unit_price.amount);
    if (billingInterval === 'year') {
      // Apply 20% discount for yearly billing
      return Math.floor(amount * 12 * 0.8);
    }
    return amount;
  };

  const getPriceDisplay = () => {
    const price = getPrice();
    if (isFree) return '$0';
    return `$${price}`;
  };

  const getBillingText = () => {
    if (isFree) return '/month';
    return billingInterval === 'year' ? '/year' : '/month';
  };

  const getCreditsOptions = () => {
    if (plan.name === 'Pro') {
      return [
        {credits: '100', price: 25},
        {credits: '200', price: 45},
        {credits: '400', price: 85},
        {credits: '800', price: 165},
        {credits: '1200', price: 245},
        {credits: '2000', price: 395},
        {credits: '3000', price: 585},
        {credits: '4000', price: 775},
      ];
    }
    return [];
  };

  const renderCreditsDropdown = () => {
    const options = getCreditsOptions();
    if (options.length === 0) return null;

    return (
      <View style={styles.creditsDropdown}>
        <View style={styles.dropdownHeader}>
          <TextElement fontSize="sm" color="SECONDARY" fontWeight="600">
            {credits} credits / month
          </TextElement>
          <MaterialCommunityIcons name="chevron-down" size={16} color={theme.SECONDARY_COLOR} />
        </View>
        {/* In a real implementation, this would be a proper dropdown */}
      </View>
    );
  };

  return (
    <Pressable
      style={[styles.card, isSelected && styles.cardSelected, isPopular && styles.cardPopular]}
      onPress={onSelect}>
      {isPopular && (
        <View style={styles.popularBadge}>
          <TextElement fontSize="xs" color="PRIMARY" fontWeight="600">
            POPULAR
          </TextElement>
        </View>
      )}

      <View style={[styles.cardHeader, {marginTop: isPopular ? 16 : 0}]}>
        <TextElement fontSize="xl" fontWeight="600" color="SECONDARY">
          {plan.name}
        </TextElement>
        <TextElement fontSize="sm" color="EDITOR_LIGHT_BLACK" style={styles.description}>
          {plan.description}
        </TextElement>
      </View>

      <View style={styles.priceSection}>
        <View style={styles.priceContainer}>
          <TextElement fontSize="4xl" fontWeight="700" color="SECONDARY">
            {getPriceDisplay()}
          </TextElement>
          <TextElement fontSize="lg" color="EDITOR_LIGHT_BLACK">
            {getBillingText()}
          </TextElement>
        </View>

        {!isFree && (
          <TextElement fontSize="sm" color="EDITOR_LIGHT_BLACK" style={styles.creditsText}>
            {credits} credits / month
          </TextElement>
        )}

        {plan.name === 'Pro' && renderCreditsDropdown()}
      </View>

      <View style={styles.featuresSection}>
        <TextElement fontSize="sm" color="SECONDARY" fontWeight="600" style={styles.featuresTitle}>
          {isFree
            ? 'Get started with:'
            : plan.name === 'Teams'
            ? 'Everything in Pro, plus:'
            : 'For more projects and usage'}
        </TextElement>

        <View style={styles.featuresList}>
          {features.map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <MaterialCommunityIcons name="check" size={16} color={theme.SUCCESS} style={styles.checkIcon} />
              <TextElement fontSize="sm" color="SECONDARY" style={styles.featureText}>
                {feature}
              </TextElement>
            </View>
          ))}
        </View>
      </View>

      <View style={styles.buttonSection}>
        {isFree ? (
          <View style={styles.currentPlanIndicator}>
            <TextElement fontSize="sm" color="SUCCESS" fontWeight="600">
              Current plan
            </TextElement>
          </View>
        ) : (
          <Button
            color={isSelected ? 'CTA' : 'SECONDARY'}
            variant="FILLED-PILL"
            size="MEDIUM"
            onPress={onSelect}
            containerStyles={styles.selectButton}>
            {isSelected ? 'Selected' : 'Select plan'}
          </Button>
        )}
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  card: {
    width: 280,
    backgroundColor: theme.DEFAULT_COLOR,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: theme.QUATERNARY_BACKGROUND,
    padding: 24,
    position: 'relative',
    minHeight: 400,
  },
  cardSelected: {
    borderColor: theme.CTA,
    shadowColor: theme.CTA,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  cardPopular: {
    borderColor: theme.CTA,
  },
  popularBadge: {
    position: 'absolute',
    top: -1,
    left: 24,
    right: 24,
    backgroundColor: theme.CTA,
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    alignItems: 'center',
  },
  cardHeader: {
    marginBottom: 24,
  },
  description: {
    marginTop: 4,
  },
  priceSection: {
    marginBottom: 24,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
  },
  creditsText: {
    marginBottom: 8,
  },
  creditsDropdown: {
    borderWidth: 1,
    borderColor: theme.QUATERNARY_BACKGROUND,
    borderRadius: 8,
    padding: 12,
  },
  dropdownHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  featuresSection: {
    flex: 1,
    marginBottom: 24,
  },
  featuresTitle: {
    marginBottom: 12,
  },
  featuresList: {
    gap: 8,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  checkIcon: {
    marginRight: 8,
    marginTop: 2,
  },
  featureText: {
    flex: 1,
    lineHeight: 20,
  },
  buttonSection: {
    marginTop: 'auto',
  },
  selectButton: {
    width: '100%',
  },
  currentPlanIndicator: {
    alignItems: 'center',
    paddingVertical: 12,
  },
});

export default PlanCard;

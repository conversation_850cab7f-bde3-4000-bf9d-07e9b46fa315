import {LogRocket} from 'apptile-core';
import React, {useEffect} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {Route, Routes, useNavigate} from 'react-router';
import {Navigate} from 'react-router-dom';
import {fetchOrgs, handleGetBlueprints, userInit} from '../../actions/editorActions';
import {EditorRootState} from '../../store/EditorRootState';
import {BuildManagerRouter} from '../buildManager';
import TermsOfUse from './pages/TermsOfUse';
import PrivacyPolicy from './pages/PrivacyPolicy';
import AnalyticsPage from './analytics/AnalyticsPage';
import NotificationsPage from './notifications/NotificationsPage';

// Initialize LogRocket globally at the earliest point
try {
  const tileDevLogRocketKey = process.env.LOGROCKET_KEY || '97heiy/apptile-staging-tiledev';
  LogRocket.init(tileDevLogRocketKey as string);
  console.log('LogRocket initialized globally');
} catch (err) {
  console.error('Global initialization of LogRocket failed:', err);
}

import {APPTILE_TILE_ENV} from '../../../.env.json';
import SupabaseCallback from '../../integrations/supabase/components/SupabaseCallback';
import {getOrgId} from '../../selectors/OnboardingSelector';
import {AppForkResolver} from '../editor/components/AppForkResolver';
import {RemoveTrailingSlash} from '../editor/components/RemoveTrailingSlash';
import EditorContainer from '../editor/containers/EditorContainer';
import {IntercomUpdate} from '../platform/components/IntercomUpdate';
import {LogRocketIdentify} from '../platform/components/LogRocketIdentify';
import {SegmentIdentify} from '../platform/components/SegmentIdentify';
import NetworkUnavailable from '../platform/pages/NetworkUnavailable';
import NotFound from '../platform/pages/NotFound';
import PlatformLoader from '../platform/pages/PlatformLoader';
import PromptToApp from '../prompt-to-app';
import {AppBranchResolver} from './components/AppBranchResolver';
import {OrgResolver} from './components/OrgResolver';
import DashboardContainer from './dashboard/DashboardContainer';
import TileEditorContainer from './editor/containers/TileEditorContainer';
import {IntegrationRouter} from './integrations';
import AuthFailed from './components/AuthFailed';
import Login from '../auth/Login';
import TileCodeEditorContainer from './editor/containers/TileCodeEditorContainer';

const TilePlatformRouter: React.FC = ({}) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {userLoggedIn, userFetched, user, networkReachable, userFetching} = useSelector(
    (state: EditorRootState) => state.user,
  );
  const orgId = useSelector(getOrgId);
  const {appId} = useSelector((state: EditorRootState) => state.apptile);
  const {appsById} = useSelector((state: EditorRootState) => state.orgs);
  const platformType = appsById?.[appId as string]?.platformType;

  useEffect(() => {
    dispatch(userInit());
    dispatch(fetchOrgs());
  }, [dispatch]);

  useEffect(() => {
    if (platformType) {
      dispatch(handleGetBlueprints(platformType));
    }
  }, [dispatch, platformType]);

  // Add this effect to identify the user when they log in
  useEffect(() => {
    if (user && userFetched && userLoggedIn) {
      try {
        console.log('Identifying user in LogRocket:', user.id);
        LogRocket.identify(user.id, {
          name: `${user.firstname} ${user.lastname}`,
          email: user.email,
          buildnumber: (window as any).BN_FOR_LOGROCKET,
          environment: process.env.NODE_ENV,
        });
        console.log('LogRocket user identified');
      } catch (err) {
        console.error('Failed to identify user in LogRocket:', err);
      }
    }
  }, [user, userLoggedIn, userFetched]);

  return (
    <>
      {userFetching && <PlatformLoader />}
      {networkReachable && (
        <PlatformRoutes userFetched={userFetched} userLoggedIn={userLoggedIn} user={user} orgId={orgId} />
      )}
      {!networkReachable && <NetworkUnavailable />}
    </>
  );
};

const PlatformRoutes = (props: any) => {
  const {userFetched, userLoggedIn, user, orgId} = props;
  return (
    <>
      <RemoveTrailingSlash />
      <Routes>
        <Route path="/supabase-callback" element={<SupabaseCallback />} />

        {/* Public routes accessible to all users */}
        <Route path="/terms-of-use" element={<TermsOfUse />} />
        <Route path="/privacy-policy" element={<PrivacyPolicy />} />
        <Route path="/unauthorized" element={<AuthFailed />} />
        <Route path="/legacy-login" element={<Login />} />

        {userFetched && userLoggedIn && (
          <>
            <Route path="/" element={<OrgResolver />} />
            <Route path="/dashboard" element={<OrgResolver />} />
            <Route path="/dashboard/:orgId" element={<DashboardContainer />} />
            <Route path="/dashboard/:orgId/app/:id" element={<AppForkResolver />} />
            <Route path="/dashboard/:orgId/app/:id/f/:forkId" element={<AppBranchResolver />} />
            <Route path="/dashboard/:orgId/app/:id/f/:forkId/b/:branchName/studio" element={<EditorContainer />} />
            <Route path="/dashboard/:orgId/app/:id/f/:forkId/b/:branchName/builds/*" element={<BuildManagerRouter />} />
            <Route
              path="/dashboard/:orgId/app/:id/f/:forkId/b/:branchName/dashboard/codeEditor/:pluginName"
              element={<TileCodeEditorContainer />}
            />
            <Route
              path="/dashboard/:orgId/app/:id/f/:forkId/b/:branchName/dashboard/editor/*"
              element={APPTILE_TILE_ENV == 'local' ? <EditorContainer /> : <TileEditorContainer />}
            />
            <Route
              path="/dashboard/:orgId/app/:id/f/:forkId/b/:branchName/dashboard/integrations/*"
              element={<IntegrationRouter />}
            />
            <Route
              path="/dashboard/:orgId/app/:id/f/:forkId/b/:branchName/dashboard/analytics/*"
              element={<AnalyticsPage />}
            />
            <Route
              path="/dashboard/:orgId/app/:id/f/:forkId/b/:branchName/dashboard/notifications/*"
              element={<NotificationsPage />}
            />

            {/* <Route path="/dashboard/*" element={<DashboardContainer />} /> */}
            <Route path="*" element={<NotFound />} />
          </>
        )}

        {userFetched && !userLoggedIn && (
          <>
            <Route path="/" element={<PromptToApp />} />
            <Route path="/dashboard/*" element={<Navigate to="/login" replace />} />
            <Route path="*" element={<NotFound />} />
          </>
        )}
      </Routes>

      {/* {userFetched && userLoggedIn && (
        <>
          <ProductFruits
            workspaceCode="0WeCURB2xuMZzmYZ"
            language="en"
            user={{
              username: user?.email as string,
              email: user?.email,
              firstname: user?.firstname,
              lastname: user?.lastname,
            }}
          />
        </>
      )} */}
      {/* All the analytics identify calls */}
      {orgId && (
        <>
          <SegmentIdentify orgId={orgId} />
          {/* <IntercomUpdate orgId={orgId} /> */}
          <LogRocketIdentify orgId={orgId} />
        </>
      )}
    </>
  );
};

export default TilePlatformRouter;

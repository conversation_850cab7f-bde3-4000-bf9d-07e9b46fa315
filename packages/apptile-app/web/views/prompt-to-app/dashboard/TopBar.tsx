import Analytics from '@/root/web/lib/segment';
import {
  MaterialCommunityIcons,
  ScreenConfig,
  getNavigationContext,
  initApptileIsEditable,
  initApptileIsPreview,
  useCallbackRef,
  useIsPreview,
} from 'apptile-core';
import _ from 'lodash';
import React, {useEffect, useState} from 'react';
import {Image, Pressable, StyleSheet, Text, View} from 'react-native';
import Animated, {Easing, useAnimatedStyle, useSharedValue, withTiming} from 'react-native-reanimated';
import {batch, useDispatch, useSelector} from 'react-redux';
import {useSearchParams} from 'react-router-dom';
import {
  EDITOR_CLOSE_CHAT_VIEW,
  EDITOR_SELECTED_PAGE_TYPE,
  EDITOR_SELECT_NAV_COMPONENT,
  editorSetActiveAttachmentId,
  editorSetActiveAttachmentKey,
  saveAppState,
  softRestartConfig,
} from '../../../actions/editorActions';
import {changeOnboardingMetadata} from '../../../actions/onboardingActions';
import {APP_PREVIEW_CLICKED} from '../../../common/onboardingConstants';
import Button from '../../../components-v2/base/Button';
import PopoverComponent from '../../../components-v2/base/Popover';
import ScreenEntityPicker from '../../../components-v2/ScreenEntityPicker';
import {useNavigate, useParams} from '../../../routing.web';
import {selectScreensInNav} from '../../../selectors/EditorSelectors';
import {getOnboardingMetadataWithKey} from '../../../selectors/OnboardingSelector';
import {EditorRootState} from '../../../store/EditorRootState';
import {getTheme, getCommonStyles} from '@/root/web/utils/themeSelector';
const theme = getTheme();
const commonStyles = getCommonStyles();

import SaveAndPublish from './SaveAndPublish';

export const TopBar = () => {
  const dispatch = useDispatch();
  const isPreview = useIsPreview();
  const onSave = useCallbackRef(() => {
    dispatch(saveAppState(false, true));
  });
  const params = useParams();
  const [searchParams] = useSearchParams();
  const fromPaymentConfirmation = searchParams.get('fromPaymentConfirmation');

  const navigate = useNavigate();
  const previewClicked = useSelector((state: EditorRootState) =>
    getOnboardingMetadataWithKey(state, APP_PREVIEW_CLICKED),
  );
  const toggleIsPreview = (_isPreview: boolean) => {
    dispatch(initApptileIsPreview(_isPreview));
    dispatch(initApptileIsEditable(!_isPreview));
    // dispatch({
    //   type: EDITOR_CLOSE_CHAT_VIEW,
    // });
    if (typeof previewClicked !== undefined && !previewClicked) {
      dispatch(changeOnboardingMetadata({[APP_PREVIEW_CLICKED]: true}));
    }
  };
  const context = getNavigationContext();
  const onPublish = useCallbackRef(() => {
    dispatch(saveAppState(true, true, 'Updated template'));
  });
  const [showPopover, setShowPopover] = useState(false);

  const slideAnimationHeight = useSharedValue(0);
  useEffect(() => {
    slideAnimationHeight.value = withTiming(showPopover ? 198 : 0, {
      duration: 100,
      easing: Easing.ease,
    });
  }, [showPopover, slideAnimationHeight]);

  const fadeEffect = useSharedValue(1);
  useEffect(() => {
    fadeEffect.value = withTiming(showPopover ? 1 : 0, {
      duration: 120,
      easing: Easing.ease,
    });
  }, [showPopover, fadeEffect]);

  const popOverAnimation = useAnimatedStyle(() => ({
    height: slideAnimationHeight.value,
    opacity: fadeEffect.value,
  }));

  const screens: ScreenConfig[] = useSelector(selectScreensInNav);
  const activeNavigation = useSelector(s => (s as any).activeNavigation);
  const activeScreen = screens?.filter((e: ScreenConfig) => e.screen == activeNavigation?.activePageId);

  //For animating topbar
  const topBarHeight = useSharedValue(54);
  useEffect(() => {
    topBarHeight.value = withTiming(isPreview ? 0 : 54, {
      duration: 680,
      easing: Easing.bezier(0.25, 0.1, 0.25, 1),
    });
  }, [isPreview, topBarHeight]);
  const topBarTopAnimation = useAnimatedStyle(() => ({
    height: topBarHeight.value,
  }));

  const editorPreviewClicked = () => {
    Analytics.track('editor:editor_previewClicked');
    toggleIsPreview(true);
  };
  const isPDP = activeScreen && !_.isEmpty(activeScreen.filter(s => s?.name === 'Product'));
  const isPLP = activeScreen && !_.isEmpty(activeScreen.filter(s => s?.name === 'Collection'));

  const currentBranch = useSelector((state: EditorRootState) => state.branches.branchesByNameWithOta)[
    params.branchName
  ];

  const onSoftRefreshEditor = () => {
    dispatch(softRestartConfig());
  };

  return (
    <Animated.View style={[styles.wrapper, topBarTopAnimation]}>
      <View style={[styles.leftSectionWrapper]}>
        <View style={styles.sidebarHeader}>
          <Pressable
            style={{flex: 1, justifyContent: 'center'}}
            onPress={() => {
              navigate(`/dashboard/${params.orgId}/app/${params.id}`);
            }}>
            <Image
              style={styles.logo}
              source={require('@/root/web/assets/images/apptile_icon.png')}
              resizeMode="cover"
            />
          </Pressable>
        </View>
        {(isPDP || isPLP) && (
          <View style={[{width: 200}]}>
            <ScreenEntityPicker isV2={true} />
          </View>
        )}
      </View>

      {/* Center section with Page selector */}
      <View style={styles.centerSection}>
        <View style={[styles.appContainerHeader]}>
          {activeScreen.length > 0 ? (
            <PopoverComponent
              visible={showPopover}
              onVisibleChange={setShowPopover}
              trigger={
                <Pressable
                  style={[styles.popoverPressableStyles]}
                  onPress={() => {
                    setShowPopover(!showPopover);
                  }}>
                  {/* <Text style={[commonStyles.baseText, styles.appContainerHeaderPreText]}>Page:</Text> */}
                  <Text style={[commonStyles.baseText, styles.appContainerHeaderText, {flex: 1}]}>
                    {_.capitalize(activeScreen[0].title || activeScreen[0].name || 'Loading')}
                  </Text>
                  <MaterialCommunityIcons name="chevron-down" size={16} color="#979797" />
                </Pressable>
              }>
              {screens && (
                <Animated.View style={[styles.appContainerHeader, styles.appContainerHeaderPopover, popOverAnimation]}>
                  <View style={{flex: 1, overflow: 'scroll', padding: 12}}>
                    {screens?.map((s: ScreenConfig, index: number) => (
                      <View key={s.name + '_' + index}>
                        <Pressable
                          style={styles.appPopoverScreenText}
                          onPress={() => {
                            batch(() => {
                              dispatch(editorSetActiveAttachmentId(''));
                              dispatch(editorSetActiveAttachmentKey(''));
                              dispatch({type: EDITOR_SELECT_NAV_COMPONENT, payload: s.name});
                              dispatch({type: EDITOR_SELECTED_PAGE_TYPE, payload: s.type});
                            });
                            context.navigate(s.name);
                            setShowPopover(false);
                          }}>
                          <View style={{width: 20}}>
                            <MaterialCommunityIcons
                              name={activeScreen.length && activeScreen[0].name == s.name ? 'check' : 'blank'}
                              size={16}
                              color={theme.PRIMARY_COLOR}
                            />
                          </View>
                          <Text style={[commonStyles.baseText, {color: '#979797', fontFamily: 'General Sans', fontSize: 18, fontWeight: '500'}]}>{_.capitalize(s.title || s.name)}</Text>
                        </Pressable>
                      </View>
                    ))}
                  </View>
                </Animated.View>
              )}
            </PopoverComponent>
          ) : (
            <Text style={[styles.appContainerHeaderText]}>Loading</Text>
          )}
        </View>
        {/* <Pressable
          style={[styles.popoverPressableStyles, styles.flexRow, styles.refreshPressable]}
          onPress={onSoftRefreshEditor}>
          <MaterialCommunityIcons name="refresh" size={28} color={theme.DEFAULT_COLOR} />
        </Pressable> */}
      </View>

      {/* Right section with Save, Preview and Launch buttons */}
      <View style={styles.rightSection}>
        <SaveAndPublish fromPaymentConfirmation={fromPaymentConfirmation} onSave={onSave} onPublish={onPublish} />
        <View style={styles.previewRefreshContainer}>
          <Button
            icon="cellphone"
            variant="FILLED"
            color="DEFAULT"
            containerStyles={styles.previewButtonWrapper}
            onPress={editorPreviewClicked}>
            Preview
          </Button>
          <Button
            icon=""
            variant="FILLED"
            color="DEFAULT"
            containerStyles={styles.launchButtonWrapper}
            onPress={() => {}}>
            Launch
          </Button>
        </View>
      </View>

      <View
        style={{
          width: '100%',
          borderBottomWidth: 1,
          borderColor: '#3A3A3A',
          position: 'absolute',
          bottom: 0,
          left: 80,
        }}
      />
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    height: 54,
    backgroundColor: '#2E2E2E',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    overflow: 'hidden',
  },
  leftSectionWrapper: {
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
    height: '100%',
  },
  centerSection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  refreshPressable: {
    padding: 12,
    borderRadius: 4,
  },
  previewRefreshContainer: {
    flexDirection: 'row',
    gap: 10,
    alignItems: 'center',
  },
  previewButtonWrapper: {
    width: 117,
    borderRadius: 8,
    backgroundColor: '#4A4A4A',
    borderColor: '#4A4A4A',
  },
  launchButtonWrapper: {
    width: 117,
    borderRadius: 8,
    backgroundColor: '#0062FF',
    borderColor: '#0062FF',
  },
  logo: {
    width: 40,
    height: 40,
  },
  pageText: {
    fontWeight: '500',
    color: '#FFFFFF',
    fontSize: 14,
  },
  sidebarHeader: {
    paddingRight: 23,
    overflow: 'hidden',
    borderRightWidth: 1,
    borderColor: '#3A3A3A',
    height: '100%',
  },
  appPopoverScreenText: {
    flexDirection: 'row',
    paddingVertical: 10,
    textAlign: 'left',
  },
  popoverPressableStyles: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%', // Ensure the Pressable takes full width of its container
  },
  appContainerHeaderText: {
    whiteSpace: 'nowrap',
    wordBreak: 'break-all',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    color: '#979797',
    fontSize: 18,
    fontWeight: '500',
    lineHeight: 24.3, // 1.35em of 18px
    letterSpacing: 0.36, // 2% of 18px
    fontFamily: 'General Sans',
    paddingVertical: 8,
  },
  appContainerHeaderPreText: {
    whiteSpace: 'nowrap',
    color: '#979797',
    fontSize: 18,
    fontWeight: '500',
    lineHeight: 24.3, // 1.35em of 18px
    letterSpacing: 0.36, // 2% of 18px
    fontFamily: 'General Sans',
    paddingVertical: 8,
  },
  appContainerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#3A3A3A',
    borderRadius: 10,
    backgroundColor: '#121212',
    paddingLeft: 12,
    paddingRight: 12,
    height: 44,
    zIndex: 2,
    minWidth: 131,
  },
  appContainerHeaderPopover: {
    zIndex: 1,
    width: 200,
    flexDirection: 'column',
    alignItems: 'flex-start',
    height: 175,
    overflow: 'hidden',
    padding: 0,
    boxShadow: '0px 4px 5px 2px rgba(0, 0, 0, 0.25)',
    opacity: 1,
    marginLeft: 45,
    marginTop: 10,
    borderRadius: 10,
    backgroundColor: '#121212',
  },
  flexRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 10,
  },
  currentBranchWrapper: {
    borderRadius: 4,
    borderWidth: 1,
    borderColor: '#3A3A3A',
    width: 119,
    // height: 34,
    overflow: 'hidden',
    justifyContent: 'center',
    paddingLeft: 16,
    // alignItems: 'center',
  },
  currentItemText: {
    whiteSpace: 'nowrap',
    wordBreak: 'break-all',
    textOverflow: 'ellipsis',
    overflow: 'hidden',
    justifyContent: 'center',
    fontWeight: '400',
    fontSize: 14,
    color: '#FFFFFF',
  },
});
